#!/usr/bin/env python3
"""
Simple test to check requirement_rules serialization in component definition.
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'workflow-service'))

def test_component_definition():
    """Test the component definition directly."""
    
    try:
        from app.components.data_interaction.api_request import ApiRequestNode
        from app.models.workflow_builder.components import InputRequirementRule
        
        print("=== Testing Component Definition ===")
        
        # Get the component definition
        definition = ApiRequestNode.get_definition()
        
        if definition is None:
            print("ERROR: Definition is None")
            return False
        
        print(f"Component: {definition['name']}")
        
        # Find the body input
        body_input = None
        for inp in definition['inputs']:
            if inp['name'] == 'body':
                body_input = inp
                break
        
        if body_input is None:
            print("ERROR: Body input not found")
            return False
        
        print(f"\nBody input requirement_rules: {body_input.get('requirement_rules')}")
        print(f"Body input requirement_logic: {body_input.get('requirement_logic')}")
        
        # Check if requirement_rules exist and are properly formatted
        req_rules = body_input.get('requirement_rules')
        if req_rules is None:
            print("ERROR: requirement_rules is None")
            return False
        
        if not isinstance(req_rules, list):
            print(f"ERROR: requirement_rules is not a list, it's {type(req_rules)}")
            return False
        
        if len(req_rules) == 0:
            print("ERROR: requirement_rules is empty")
            return False
        
        print(f"Found {len(req_rules)} requirement rules")
        
        # Check each rule
        for i, rule in enumerate(req_rules):
            print(f"Rule {i}: {rule}")
            if not isinstance(rule, dict):
                print(f"ERROR: Rule {i} is not a dict, it's {type(rule)}")
                return False
            
            required_fields = ['field_name', 'field_value', 'operator']
            for field in required_fields:
                if field not in rule:
                    print(f"ERROR: Rule {i} missing field '{field}'")
                    return False
        
        print("✓ All requirement rules are properly formatted")
        return True
        
    except Exception as e:
        print(f"ERROR: Exception occurred: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_component_service():
    """Test the component service processing."""
    
    try:
        from app.services.workflow_builder.component_service import ComponentService
        
        print("\n=== Testing Component Service ===")
        
        service = ComponentService()
        
        # Get the raw component definition
        from app.components.data_interaction.api_request import ApiRequestNode
        raw_definition = ApiRequestNode.get_definition()
        
        # Process it through the component service
        processed_def = service._get_component_definition_from_class(ApiRequestNode)
        
        print(f"Processed component: {processed_def.name}")
        
        # Find the body input
        body_input = None
        for inp in processed_def.inputs:
            if inp.name == 'body':
                body_input = inp
                break
        
        if body_input is None:
            print("ERROR: Body input not found in processed definition")
            return False
        
        print(f"Body input requirement_rules: {body_input.requirement_rules}")
        print(f"Body input requirement_logic: {body_input.requirement_logic}")
        
        # Check if requirement_rules exist
        if body_input.requirement_rules is None:
            print("ERROR: requirement_rules is None in processed definition")
            return False
        
        if len(body_input.requirement_rules) == 0:
            print("ERROR: requirement_rules is empty in processed definition")
            return False
        
        print(f"Found {len(body_input.requirement_rules)} requirement rules in processed definition")
        
        # Check each rule
        for i, rule in enumerate(body_input.requirement_rules):
            print(f"Processed Rule {i}: {rule}")
            print(f"  Type: {type(rule)}")
            if hasattr(rule, 'field_name'):
                print(f"  Field name: {rule.field_name}")
                print(f"  Field value: {rule.field_value}")
                print(f"  Operator: {rule.operator}")
        
        print("✓ Component service processing successful")
        return True
        
    except Exception as e:
        print(f"ERROR: Exception in component service test: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("Testing requirement_rules serialization...")
    
    success1 = test_component_definition()
    success2 = test_component_service()
    
    if success1 and success2:
        print("\n✓ All tests passed!")
    else:
        print("\n✗ Some tests failed!")
        sys.exit(1)
