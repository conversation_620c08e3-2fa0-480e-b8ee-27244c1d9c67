#!/usr/bin/env python3
"""
Test the full flow from component definition to gRPC to API response.
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'workflow-service'))

def test_protobuf_conversion():
    """Test the protobuf conversion specifically."""
    
    try:
        from app.services.workflow_builder.workflow_builder_service import WorkflowBuilderService
        from app.services.workflow_builder.component_service import ComponentService
        import asyncio
        
        print("=== Testing Protobuf Conversion ===")
        
        # Create component service and discover components
        component_service = ComponentService()
        components = asyncio.run(component_service.discover_components())
        
        # Find the API Request component
        api_request_comp = None
        for category, comps in components.items():
            if "ApiRequestNode" in comps:
                api_request_comp = comps["ApiRequestNode"]
                break
        
        if api_request_comp is None:
            print("ERROR: ApiRequestNode not found")
            return False
        
        # Find the body input
        body_input = None
        for inp in api_request_comp.inputs:
            if inp.name == 'body':
                body_input = inp
                break
        
        if body_input is None:
            print("ERROR: Body input not found")
            return False
        
        print(f"Original requirement_rules: {body_input.requirement_rules}")
        print(f"Original requirement_logic: {body_input.requirement_logic}")
        
        # Now test the gRPC service conversion
        grpc_service = WorkflowBuilderService()
        
        # Create a mock request
        class MockRequest:
            force_refresh = False
        
        class MockContext:
            pass
        
        # Call the discoverComponents method
        response = grpc_service.discoverComponents(MockRequest(), MockContext())
        
        # Find the API Request component in the response
        api_request_proto = None
        for comp in response.components:
            if comp.name == "ApiRequestNode":
                api_request_proto = comp
                break
        
        if api_request_proto is None:
            print("ERROR: ApiRequestNode not found in gRPC response")
            return False
        
        # Find the body input in the protobuf response
        body_input_proto = None
        for inp in api_request_proto.inputs:
            if inp.name == 'body':
                body_input_proto = inp
                break
        
        if body_input_proto is None:
            print("ERROR: Body input not found in gRPC response")
            return False
        
        print(f"Protobuf requirement_rules: {list(body_input_proto.requirement_rules)}")
        print(f"Protobuf requirement_logic: {body_input_proto.requirement_logic}")
        
        if not body_input_proto.requirement_rules:
            print("ERROR: No requirement rules found in protobuf response!")
            return False
        
        print(f"Number of protobuf requirement rules: {len(body_input_proto.requirement_rules)}")
        
        for i, rule in enumerate(body_input_proto.requirement_rules):
            print(f"  Protobuf Rule {i}:")
            print(f"    Field name: {rule.field_name}")
            print(f"    Field value: {rule.field_value}")
            print(f"    Operator: {rule.operator}")
        
        # Now test the API Gateway conversion
        print("\n=== Testing API Gateway Conversion ===")
        
        # Simulate the API Gateway conversion logic
        requirement_rules = None
        if body_input_proto.requirement_rules:
            requirement_rules = []
            for rule in body_input_proto.requirement_rules:
                requirement_rules.append({
                    "field_name": rule.field_name,
                    "field_value": rule.field_value,
                    "operator": rule.operator
                })
        
        print(f"API Gateway converted requirement_rules: {requirement_rules}")
        
        if requirement_rules is None:
            print("ERROR: API Gateway conversion resulted in None!")
            return False
        
        if len(requirement_rules) == 0:
            print("ERROR: API Gateway conversion resulted in empty list!")
            return False
        
        print("✓ Full flow test successful!")
        return True
        
    except Exception as e:
        print(f"ERROR: Exception in full flow test: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("Testing full flow from component definition to API response...")
    
    success = test_protobuf_conversion()
    
    if success:
        print("\n✓ Full flow test passed!")
    else:
        print("\n✗ Full flow test failed!")
        sys.exit(1)
