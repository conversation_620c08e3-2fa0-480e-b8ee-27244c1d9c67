{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_94a4b977._.js", "server/edge/chunks/[root of the server]__1daf8b13._.js", "server/edge/chunks/edge-wrapper_0cc0fab8.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next|_vercel|.*\\.[\\w]+$).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next|_vercel|.*\\.[\\w]+$).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "a+lIWRKRxvnVQaEcqhJqHOiJoD1n5nRfxss0mxpJlgU=", "__NEXT_PREVIEW_MODE_ID": "963ce139baa70a454af4f946ff438007", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "c2e8489ec685bf06bf047e81f3195230b88c481f8d6e232ed8030235f96a4da9", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "b26741aa06de6bc571655aceccf9528f5470a6ded9b1be0db1c098af02a9fd54"}}}, "sortedMiddleware": ["/"], "functions": {}}