#!/usr/bin/env python3
"""
Test script to investigate the requirement_rules serialization issue.
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'workflow-service'))

from app.components.data_interaction.api_request import ApiRequestNode
from app.models.workflow_builder.components import InputRequirementRule
import json

def test_api_request_definition():
    """Test the API Request component definition to see requirement_rules."""
    
    print("=== Testing API Request Component Definition ===")
    
    # Get the component definition
    definition = ApiRequestNode.get_definition()
    
    if definition is None:
        print("ERROR: Definition is None")
        return
    
    print(f"Component: {definition['name']}")
    print(f"Display Name: {definition['display_name']}")
    print(f"Number of inputs: {len(definition['inputs'])}")
    
    # Find the body input
    body_input = None
    for inp in definition['inputs']:
        if inp['name'] == 'body':
            body_input = inp
            break
    
    if body_input is None:
        print("ERROR: Body input not found")
        return
    
    print("\n=== Body Input Analysis ===")
    print(f"Name: {body_input['name']}")
    print(f"Display Name: {body_input['display_name']}")
    print(f"Required: {body_input['required']}")
    print(f"Visibility Rules: {body_input.get('visibility_rules')}")
    print(f"Visibility Logic: {body_input.get('visibility_logic')}")
    print(f"Requirement Rules: {body_input.get('requirement_rules')}")
    print(f"Requirement Logic: {body_input.get('requirement_logic')}")
    
    # Check the raw inputs from the class
    print("\n=== Raw Class Inputs Analysis ===")
    for inp in ApiRequestNode.inputs:
        if inp.name == 'body':
            print(f"Raw input name: {inp.name}")
            print(f"Raw requirement_rules: {inp.requirement_rules}")
            print(f"Raw requirement_logic: {inp.requirement_logic}")
            
            if inp.requirement_rules:
                print(f"Number of requirement rules: {len(inp.requirement_rules)}")
                for i, rule in enumerate(inp.requirement_rules):
                    print(f"  Rule {i}: {rule}")
                    print(f"    Type: {type(rule)}")
                    print(f"    Field name: {rule.field_name}")
                    print(f"    Field value: {rule.field_value}")
                    print(f"    Operator: {rule.operator}")
                    
                    # Test model_dump
                    print(f"    model_dump(): {rule.model_dump()}")
            break
    
    # Test serialization
    print("\n=== Serialization Test ===")
    try:
        json_str = json.dumps(definition, indent=2)
        print("JSON serialization successful")
        
        # Parse back and check
        parsed = json.loads(json_str)
        body_parsed = None
        for inp in parsed['inputs']:
            if inp['name'] == 'body':
                body_parsed = inp
                break
        
        if body_parsed:
            print(f"Parsed requirement_rules: {body_parsed.get('requirement_rules')}")
        
    except Exception as e:
        print(f"JSON serialization failed: {e}")

if __name__ == "__main__":
    test_api_request_definition()
