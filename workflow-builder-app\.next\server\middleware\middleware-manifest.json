{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_94a4b977._.js", "server/edge/chunks/[root of the server]__1daf8b13._.js", "server/edge/chunks/edge-wrapper_0cc0fab8.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!api|_next|_vercel|.*\\.[\\w]+$).*){(\\\\.json)}?", "originalSource": "/((?!api|_next|_vercel|.*\\.[\\w]+$).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "a+lIWRKRxvnVQaEcqhJqHOiJoD1n5nRfxss0mxpJlgU=", "__NEXT_PREVIEW_MODE_ID": "5ea8290953da43e1c40ca84083892111", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "4050dc93e7cb81e3f8bc6391e806be2c1ddd623eb13e54eb5d5d44e410a9941b", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "fe5a964ad510d973f8b240caaa49710e5cb4a4a5aca754a232030c672591c8df"}}}, "instrumentation": null, "functions": {}}